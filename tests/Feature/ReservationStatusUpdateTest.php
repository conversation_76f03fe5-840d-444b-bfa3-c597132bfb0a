<?php

namespace Tests\Feature;

use App\Models\Field;
use App\Models\Reservation;
use App\Models\User;
use Illuminate\Foundation\Testing\RefreshDatabase;
use PHPUnit\Framework\Attributes\CoversClass;
use PHPUnit\Framework\Attributes\Test;
use Tests\TestCase;

#[CoversClass(\App\Http\Controllers\ReservationController::class)]
class ReservationStatusUpdateTest extends TestCase
{
    use RefreshDatabase;

    protected User $user;
    protected Field $field;

    protected function setUp(): void
    {
        parent::setUp();

        $this->user = User::factory()->create(['role' => 'member']);
        $this->field = Field::factory()->create([
            'name' => 'Test Soccer Field',
            'type' => 'Soccer',
            'hourly_rate' => 75.00,
            'status' => 'Active',
            'opening_time' => '08:00',
            'closing_time' => '22:00',
            'min_booking_hours' => 1,
            'max_booking_hours' => 8,
        ]);
    }

    #[Test]
    public function reservation_index_automatically_updates_completed_reservations()
    {
        // Create a reservation that ended 1 hour ago
        $endedReservation = Reservation::factory()->create([
            'field_id' => $this->field->id,
            'user_id' => $this->user->id,
            'status' => 'Confirmed',
            'booking_date' => now()->subHours(2)->toDateString(),
            'start_time' => now()->subHours(2)->format('H:i'),
            'end_time' => now()->subHours(1)->format('H:i'),
        ]);

        // Create a current reservation
        $currentReservation = Reservation::factory()->create([
            'field_id' => $this->field->id,
            'user_id' => $this->user->id,
            'status' => 'Confirmed',
            'booking_date' => now()->subMinutes(30)->toDateString(),
            'start_time' => now()->subMinutes(30)->format('H:i'),
            'end_time' => now()->addMinutes(30)->format('H:i'),
        ]);

        // Verify initial statuses
        $this->assertEquals('Confirmed', $endedReservation->status);
        $this->assertEquals('Confirmed', $currentReservation->status);

        // Access the reservations index page
        $response = $this->actingAs($this->user)
            ->get(route('reservations.index'));

        $response->assertStatus(200);

        // Refresh the reservations from database
        $endedReservation->refresh();
        $currentReservation->refresh();

        // Assert that the ended reservation was automatically updated to completed
        $this->assertEquals('Completed', $endedReservation->status);
        // Assert that the current reservation remains confirmed
        $this->assertEquals('Confirmed', $currentReservation->status);
    }

    #[Test]
    public function reservation_index_only_updates_confirmed_reservations()
    {
        // Create reservations with different statuses that have all ended
        $confirmedReservation = Reservation::factory()->create([
            'field_id' => $this->field->id,
            'user_id' => $this->user->id,
            'status' => 'Confirmed',
            'booking_date' => now()->subHours(2)->toDateString(),
            'start_time' => now()->subHours(2)->format('H:i'),
            'end_time' => now()->subHours(1)->format('H:i'),
        ]);

        $pendingReservation = Reservation::factory()->create([
            'field_id' => $this->field->id,
            'user_id' => $this->user->id,
            'status' => 'Pending',
            'booking_date' => now()->subHours(2)->toDateString(),
            'start_time' => now()->subHours(2)->format('H:i'),
            'end_time' => now()->subHours(1)->format('H:i'),
        ]);

        // Access the reservations index page
        $response = $this->actingAs($this->user)
            ->get(route('reservations.index'));

        $response->assertStatus(200);

        // Refresh the reservations from database
        $confirmedReservation->refresh();
        $pendingReservation->refresh();

        // Assert that only the confirmed reservation was updated
        $this->assertEquals('Completed', $confirmedReservation->status);
        $this->assertEquals('Pending', $pendingReservation->status);
    }

    #[Test]
    public function same_day_reservations_handled_correctly_by_index()
    {
        $today = now()->toDateString();
        
        // Reservation that ended earlier today
        $endedReservation = Reservation::factory()->create([
            'field_id' => $this->field->id,
            'user_id' => $this->user->id,
            'status' => 'Confirmed',
            'booking_date' => $today,
            'start_time' => now()->subHours(3)->format('H:i'),
            'end_time' => now()->subHours(2)->format('H:i'),
        ]);

        // Reservation happening later today
        $futureReservation = Reservation::factory()->create([
            'field_id' => $this->field->id,
            'user_id' => $this->user->id,
            'status' => 'Confirmed',
            'booking_date' => $today,
            'start_time' => now()->addHours(1)->format('H:i'),
            'end_time' => now()->addHours(2)->format('H:i'),
        ]);

        // Access the reservations index page
        $response = $this->actingAs($this->user)
            ->get(route('reservations.index'));

        $response->assertStatus(200);

        // Refresh the reservations from database
        $endedReservation->refresh();
        $futureReservation->refresh();

        // Assert that only the ended reservation was updated
        $this->assertEquals('Completed', $endedReservation->status);
        $this->assertEquals('Confirmed', $futureReservation->status);
    }
}
